import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import {
  AlertTriangle,
  Calendar,
  Check,
  CheckCircle,
  CreditCard,
  DollarSign,
  Eye,
  FileText,
  Filter,
  Play,
  Search,
  Star,
  TrendingUp,
  User,
  X,
} from "lucide-react"
import { useState } from "react"

// Icon mapping for dynamic icon rendering
const iconMap = {
  Play,
  Check,
  AlertTriangle,
};

const getColorClasses = (color: string, type: "bg" | "text") => {
  const colors = {
    blue: { bg: "bg-blue-100", text: "text-blue-600" },
    amber: { bg: "bg-amber-100", text: "text-amber-600" },
    green: { bg: "bg-green-100", text: "text-green-600" },
    red: { bg: "bg-red-100", text: "text-red-600" },
  }
  return colors[color as keyof typeof colors]?.[type] || colors.blue[type]
}

const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case "high":
      return "bg-red-100 text-red-800"
    case "medium":
      return "bg-amber-100 text-amber-800"
    case "low":
      return "bg-green-100 text-green-800"
    case "urgent":
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case "approved":
      return "bg-green-100 text-green-800"
    case "under review":
      return "bg-amber-100 text-amber-800"
    case "rejected":
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

const getRecommendationColor = (recommendation: string) => {
  switch (recommendation.toLowerCase()) {
    case "approve":
      return "bg-green-100 text-green-800"
    case "review":
      return "bg-amber-100 text-amber-800"
    case "reject":
      return "bg-red-100 text-red-800"
    default:
      return "bg-gray-100 text-gray-800"
  }
}

export default function Requests() {
  const [selectedRequest, setSelectedRequest] = useState<any>(null)
  const [isDecisionReportOpen, setIsDecisionReportOpen] = useState(false)
  const queryClient = useQueryClient()

  // Mutation for updating request status
  const updateRequestMutation = useMutation({
    mutationFn: async ({ requestId, status }: { requestId: string, status: string }) => {
      // Extract numeric ID from formatted ID (e.g., "#REQ-123" -> 123)
      const numericId = parseInt(requestId.replace('#REQ-', ''))

      const response = await fetch(`/api/requests/${numericId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          status,
          reviewedBy: 'Admin1' // You might want to get this from user context
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update request')
      }

      return response.json()
    },
    onSuccess: () => {
      // Invalidate and refetch the requests data
      queryClient.invalidateQueries({ queryKey: ["/api/requests"] })
      queryClient.invalidateQueries({ queryKey: ["/api/requests/stats"] })
    },
  })

  const { data: requestStats, isLoading: statsLoading } = useQuery({
    queryKey: ["/api/requests/stats"],
  });

  const { data: requests, isLoading: requestsLoading } = useQuery({
    queryKey: ["/api/requests"],
  });

  const openDecisionReport = (request: any) => {
    setSelectedRequest(request)
    setIsDecisionReportOpen(true)
  }

  if (statsLoading || requestsLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading requests...</p>
        </div>
      </div>
    );
  }

  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case "low":
        return "text-green-600 bg-green-100"
      case "medium":
        return "text-amber-600 bg-amber-100"
      case "high":
        return "text-red-600 bg-red-100"
      default:
        return "text-gray-600 bg-gray-100"
    }
  }

  return (
      <div>
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Finance Requests Management</h1>
              <p className="text-gray-600">Track and manage all finance applications</p>
            </div>
            <Dialog>
              <DialogTrigger asChild>
                <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
                  <FileText className="w-4 h-4 mr-2" />
                  Evaluation Criteria
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle className="flex items-center gap-2">
                    <Star className="w-5 h-5" />
                    Request Evaluation Criteria
                  </DialogTitle>
                </DialogHeader>
                <div className="space-y-6 py-4">
                  <div className="space-y-4">
                    <div className="border-l-4 border-blue-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <CreditCard className="w-4 h-4" />
                        POS Average Volume (Last 3 Months)
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • {"<"} 300 KWD →{" "}
                          <span className="font-semibold text-red-600">Reject: insufficient business activity</span>
                        </p>
                        <p>
                          • 300–900 KWD →{" "}
                          <span className="font-semibold text-amber-600">Review: moderate performance, check additional factors</span>
                        </p>
                        <p>
                          • {">"} 900 KWD →{" "}
                          <span className="font-semibold text-green-600">Approve: strong POS activity</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-green-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <TrendingUp className="w-4 h-4" />
                        POS Growth Rate (3 Months)
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • {"<"} -10% →{" "}
                          <span className="font-semibold text-red-600">Reject: negative trend indicates declining business</span>
                        </p>
                        <p>
                          • -10% to +10% →{" "}
                          <span className="font-semibold text-amber-600">Review: neutral growth, verify with other indicators</span>
                        </p>
                        <p>
                          • {">"} +10% →{" "}
                          <span className="font-semibold text-green-600">Approve: healthy growth trajectory</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-red-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <AlertTriangle className="w-4 h-4" />
                        POS Chargeback Rate
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • {">"} 2% →{" "}
                          <span className="font-semibold text-red-600">Reject: high risk of transactional disputes</span>
                        </p>
                        <p>
                          • 1–2% →{" "}
                          <span className="font-semibold text-amber-600">Review: acceptable risk if supported by strong financials</span>
                        </p>
                        <p>
                          • {"<"} 1% →{" "}
                          <span className="font-semibold text-green-600">Approve: low chargeback risk</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-purple-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <DollarSign className="w-4 h-4" />
                        Monthly Inflow
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • {"<"} 600 KWD →{" "}
                          <span className="font-semibold text-red-600">Reject: insufficient revenue flow</span>
                        </p>
                        <p>
                          • ≥ 600 KWD →{" "}
                          <span className="font-semibold text-green-600">Approve: acceptable level of business income</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-amber-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <TrendingUp className="w-4 h-4" />
                        Inflow / Outflow Ratio
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • {"<"} 0.85 →{" "}
                          <span className="font-semibold text-red-600">Reject: cash flow instability</span>
                        </p>
                        <p>
                          • 0.85–1.0 →{" "}
                          <span className="font-semibold text-amber-600">Review: stable but needs support from other indicators</span>
                        </p>
                        <p>
                          • {">"} 1.0 →{" "}
                          <span className="font-semibold text-green-600">Approve: strong cash flow surplus</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-indigo-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <DollarSign className="w-4 h-4" />
                        Current Account Balance
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • {"<"} 150 KWD →{" "}
                          <span className="font-semibold text-red-600">Reject: low liquidity</span>
                        </p>
                        <p>
                          • 150–600 KWD →{" "}
                          <span className="font-semibold text-amber-600">Review: moderate liquidity, validate with inflow/outflow</span>
                        </p>
                        <p>
                          • {">"} 600 KWD →{" "}
                          <span className="font-semibold text-green-600">Approve: good liquidity buffer</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-red-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <Calendar className="w-4 h-4" />
                        Repayment History (12 Months)
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • ≥ 2 missed repayments →{" "}
                          <span className="font-semibold text-red-600">Reject: poor repayment behavior</span>
                        </p>
                        <p>
                          • 1 missed repayment →{" "}
                          <span className="font-semibold text-amber-600">Review: acceptable if offset by strong POS or inflow</span>
                        </p>
                        <p>
                          • 0 missed repayments →{" "}
                          <span className="font-semibold text-green-600">Approve: clean repayment record</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-teal-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <CheckCircle className="w-4 h-4" />
                        Collateral to Loan Ratio
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • {"<"} 50% →{" "}
                          <span className="font-semibold text-amber-600">Review: insufficient collateral, check POS and repayment</span>
                        </p>
                        <p>
                          • 50–100% →{" "}
                          <span className="font-semibold text-green-600">Approve: adequate coverage</span>
                        </p>
                        <p>
                          • {">"} 100% →{" "}
                          <span className="font-semibold text-green-600">Approve: strong security</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-cyan-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <CheckCircle className="w-4 h-4" />
                        Credit Limit Formula
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          <span className="font-semibold">Base Limit = min(</span>
                        </p>
                        <p className="ml-4">
                          • 2 × Avg. POS volume,
                        </p>
                        <p className="ml-4">
                          • 1.5 × Monthly Inflow,
                        </p>
                        <p className="ml-4">
                          • 0.75 × Collateral Value
                        </p>
                        <p>
                          <span className="font-semibold">)</span>
                        </p>
                      </div>
                    </div>

                    <div className="border-l-4 border-gray-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        Final Decision
                      </h3>
                      <div className="text-gray-600 text-sm mt-1 space-y-1">
                        <p>
                          • <span className="font-semibold text-green-600">Approve:</span> All primary indicators meet "Approve" criteria
                        </p>
                        <p>
                          • <span className="font-semibold text-amber-600">Review:</span> 1–2 indicators fall under "Review"
                        </p>
                        <p>
                          • <span className="font-semibold text-red-600">Reject:</span> Any indicator falls under "Reject" or 3+ "Review" flags
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Request Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          {requestStats.map((stat) => {
            const Icon = stat.icon
            return (
                <Card key={stat.title} className="border border-gray-200">
                  <CardContent className="p-6">
                    <div className="flex items-center">
                      <div
                          className={`w-12 h-12 ${getColorClasses(stat.color, "bg")} rounded-lg flex items-center justify-center`}
                      >
                        <Icon className={`${getColorClasses(stat.color, "text")} w-6 h-6`} />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
            )
          })}
        </div>

        {/* Filters and Search */}
        <Card className="border border-gray-200 mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                    placeholder="Search loan applications..."
                    className="pl-10 border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              <Select>
                <SelectTrigger className="w-full sm:w-48 border-gray-300">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="review">Under Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
              <Select>
                <SelectTrigger className="w-full sm:w-48 border-gray-300">
                  <SelectValue placeholder="Sector" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="retail">Retail</SelectItem>
                  <SelectItem value="restaurant">Restaurant</SelectItem>
                  <SelectItem value="entertainment">Entertainment</SelectItem>
                </SelectContent>
              </Select>
              <Button variant="outline" className="border-gray-300 bg-transparent">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Requests Table */}
        <Card className="border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Request ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Applicant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sector
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  System Recommendation
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reviewed By
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Reviewed At
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
              {requests?.map((request: any) => (
                  <tr key={request.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{request.id}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{request.clientName}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{request.clientSector}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">{request.amount}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm  text-gray-900">{request.submissionDate}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                      <Badge
                          variant="secondary"
                          className={getRecommendationColor(request.decisionReport.recommendation)}
                      >
                        {request.decisionReport.recommendation}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Badge variant="secondary" className={getStatusColor(request.status)}>
                        {request.status}
                      </Badge>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{request.reviewedBy}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{request.reviewedAt}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-3">
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-primary hover:text-primary/80"
                            onClick={() => openDecisionReport(request)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        {/*
                      <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm" className="text-green-600 hover:text-green-900">
                        <CheckCircle className="w-4 h-4" />
                      </Button>
                      */}
                      </div>
                    </td>
                  </tr>
              ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="bg-white px-6 py-3 border-t border-gray-200 flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {requests.length} results
            </div>
            {/* <div className="flex space-x-2">
              <Button variant="outline" size="sm" disabled>
                Previous
              </Button>
              <Button variant="default" size="sm">
                1
              </Button>
              <Button variant="outline" size="sm">
                2
              </Button>
              <Button variant="outline" size="sm">
                3
              </Button>
              <Button variant="outline" size="sm">
                Next
              </Button>
            </div> */}
          </div>
        </Card>

        {/* Decision Report Dialog */}
        <Dialog open={isDecisionReportOpen} onOpenChange={setIsDecisionReportOpen}>
          <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                Loan Decision Report - {selectedRequest?.id}
              </DialogTitle>
            </DialogHeader>
            {selectedRequest && (
                <div className="space-y-6 py-4">
                  {/* Applicant Information */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                      <User className="w-4 h-4" />
                      Applicant Information
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Name:</span>
                        <span className="ml-2 font-medium">{selectedRequest.clientName}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Sector:</span>
                        <span className="ml-2 font-medium">{selectedRequest.clientSector}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Years in Business:</span>
                        <span className="ml-2 font-medium">{selectedRequest.years} years</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Amount Requested:</span>
                        <span className="ml-2 font-medium text-green-600">{selectedRequest.amount}</span>
                      </div>
                    </div>
                  </div>

                  {/* Financial Data */}
                  <div className="border-l-4 border-blue-500 pl-4">
                    <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                      <DollarSign className="w-4 h-4" />
                      Financial Data
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="space-y-2">
                        <div>
                          <span className="text-gray-600">Account Balance:</span>
                          <span className="ml-2 font-bold text-blue-600">
                        ${selectedRequest.account_balance.toLocaleString()}
                      </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Monthly Inflow:</span>
                          <span className="ml-2 font-medium text-green-600">
                        ${selectedRequest.inflow.toLocaleString()}
                      </span>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div>
                          <span className="text-gray-600">Monthly Outflow:</span>
                          <span className="ml-2 font-medium text-red-600">${selectedRequest.outflow.toLocaleString()}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Inflow/Outflow Ratio:</span>
                          <span className="ml-2 font-medium">
                        {(selectedRequest.inflow / selectedRequest.outflow).toFixed(2)}
                      </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* POS Data from Database */}
                  {selectedRequest.decisionReport.posData && (
                    <div className="border-l-4 border-orange-500 pl-4">
                      <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                        <TrendingUp className="w-4 h-4" />
                        Point of Sales Data
                      </h3>

                      {/* Client-Level POS Metrics from Database */}
                      <div className="bg-indigo-50 p-4 rounded-lg mb-4">
                        <h4 className="font-medium text-indigo-900 mb-3">Client POS Summary</h4>
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div className="text-center">
                            <div className="text-gray-600 mb-1">3-Month Avg Volume</div>
                            <div className="font-bold text-indigo-900">
                              ${selectedRequest.decisionReport.posData.avgPosVolume3Months?.toLocaleString() || 'N/A'}
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-gray-600 mb-1">3-Month Growth Rate</div>
                            <div className={`font-bold ${
                              (selectedRequest.decisionReport.posData.posGrowthRate3Months || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {((selectedRequest.decisionReport.posData.posGrowthRate3Months || 0) * 100).toFixed(1)}%
                            </div>
                          </div>
                          <div className="text-center">
                            <div className="text-gray-600 mb-1">Chargeback Rate</div>
                            <div className={`font-bold ${
                              (selectedRequest.decisionReport.posData.chargebackRate || 0) < 0.005 ? 'text-green-600' :
                              (selectedRequest.decisionReport.posData.chargebackRate || 0) < 0.01 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {((selectedRequest.decisionReport.posData.chargebackRate || 0) * 100).toFixed(3)}%
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* System Recommendation */}
                  <div className="border-l-4 border-green-500 pl-4">
                    <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                      <TrendingUp className="w-4 h-4" />
                      System Recommendation
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <span className="text-gray-600">Recommendation:</span>
                        <div className="mt-1 p-3 bg-green-50 border border-green-200 rounded-md">
                          <Badge className={getRecommendationColor(selectedRequest.decisionReport.recommendation)}>
                            {selectedRequest.decisionReport.recommendation}
                          </Badge>
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-600">Analysis Notes:</span>
                        <div className="mt-1 p-3 bg-gray-50 border border-gray-200 rounded-md">
                          <p className="text-gray-700">{selectedRequest.decisionReport.notes}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Timeline */}
                  <div className="border-l-4 border-purple-500 pl-4">
                    <h3 className="font-semibold text-gray-900 flex items-center gap-2 mb-3">
                      <Calendar className="w-4 h-4" />
                      Application Timeline
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Submission Date:</span>
                        <span className="ml-2 font-medium">{selectedRequest.submissionDate}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Current Status:</span>
                        <Badge variant="secondary" className={getStatusColor(selectedRequest.status)}>
                          {selectedRequest.status}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Admin Decision Buttons */}
                  {selectedRequest.status.toLowerCase() === "under review" && (
                      <div className="border-t pt-6">
                        <h3 className="font-semibold text-gray-900 mb-4">Admin Decision</h3>
                        <div className="flex gap-4 justify-end">
                          <Button
                              variant="outline"
                              className="border-red-300 text-red-600 hover:bg-red-50 hover:text-red-700 bg-transparent"
                              disabled={updateRequestMutation.isPending}
                              onClick={() => {
                                updateRequestMutation.mutate(
                                  { requestId: selectedRequest.id, status: "Rejected" },
                                  {
                                    onSuccess: () => {
                                      setIsDecisionReportOpen(false)
                                    },
                                    onError: (error) => {
                                      console.error("Failed to reject request:", error)
                                      // You might want to show a toast notification here
                                    }
                                  }
                                )
                              }}
                          >
                            <X className="w-4 h-4 mr-2" />
                            {updateRequestMutation.isPending ? "Rejecting..." : "Reject Request"}
                          </Button>
                          <Button
                              className="bg-green-600 hover:bg-green-700 text-white"
                              disabled={updateRequestMutation.isPending}
                              onClick={() => {
                                updateRequestMutation.mutate(
                                  { requestId: selectedRequest.id, status: "Approved" },
                                  {
                                    onSuccess: () => {
                                      setIsDecisionReportOpen(false)
                                    },
                                    onError: (error) => {
                                      console.error("Failed to approve request:", error)
                                      // You might want to show a toast notification here
                                    }
                                  }
                                )
                              }}
                          >
                            <CheckCircle className="w-4 h-4 mr-2" />
                            {updateRequestMutation.isPending ? "Approving..." : "Approve Request"}
                          </Button>
                        </div>
                      </div>
                  )}
                </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
  )
}
