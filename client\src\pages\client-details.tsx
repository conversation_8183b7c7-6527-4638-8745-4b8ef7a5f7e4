"use client"

import { <PERSON><PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useQuery } from "@tanstack/react-query"
import {
    AlertTriangle,
    ArrowLeft,
    Calendar,
    CheckCircle,
    CreditCard,
    DollarSign,
    FileText,
    Shield,
    TrendingDown,
    TrendingUp,
    User,
} from "lucide-react"
import { Link, useParams } from "wouter"


const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case "active":
            return "bg-green-100 text-green-800"
        case "inactive":
            return "bg-gray-100 text-gray-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}

const getLoanStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
        case "approved":
            return "bg-green-100 text-green-800"
        case "completed":
            return "bg-blue-100 text-blue-800"
        case "rejected":
            return "bg-red-100 text-red-800"
        default:
            return "bg-gray-100 text-gray-800"
    }
}

export default function ClientDetails() {
    const params = useParams()
    const clientId = params.id

    const { data: client, isLoading, error } = useQuery({
        queryKey: ["/api/clients", clientId],
        enabled: !!clientId,
    });

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
                    <p className="mt-2 text-gray-600">Loading client details...</p>
                </div>
            </div>
        );
    }

    if (error || !client) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="text-center">
                    <h2 className="text-2xl font-bold text-gray-900 mb-2">Client Not Found</h2>
                    <p className="text-gray-600 mb-4">The requested client could not be found.</p>
                    <Link href="/clients">
                        <Button>
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Clients
                        </Button>
                    </Link>
                </div>
            </div>
        )
    }

    return (
        <div>
            {/* Header */}
            <div className="mb-6">
                <div className="flex items-center gap-4 mb-4">
                    <Link href="/clients">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back to Clients
                        </Button>
                    </Link>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">{client.name}</h1>
                        <p className="text-gray-600">Client Details & Financial Overview</p>
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Client Information */}
                <div className="lg:col-span-1">
                    <Card className="border border-gray-200 mb-6">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <User className="w-5 h-5" />
                                Client Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-6 space-y-4">
                            <div>
                                <label className="text-sm font-medium text-gray-600">Business Name</label>
                                <p className="text-gray-900 font-medium">{client.name}</p>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-600">Sector</label>
                                <p className="text-gray-900">{client.sector}</p>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-600">Years in Business</label>
                                <p className="text-gray-900">{client.years} years</p>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-600">Status</label>
                                <div className="mt-1">
                                    <Badge variant="secondary" className={getStatusColor(client.status)}>
                                        {client.status}
                                    </Badge>
                                </div>
                            </div>

                            <div>
                                <label className="text-sm font-medium text-gray-600">Registration Date</label>
                                <p className="text-gray-900">{client.registrationDate}</p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Risk & Compliance Information */}
                    <Card className="border border-gray-200">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <Shield className="w-5 h-5" />
                                Risk & Compliance
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-6 space-y-4">
                            <div className="flex items-center gap-3">
                                <Shield className="w-4 h-4 text-gray-400" />
                                <div>
                                    <label className="text-sm font-medium text-gray-600">Collateral to Loan Ratio</label>
                                    <p className="text-gray-900 font-bold">
                                        {client.collateralToLoanRatio === null ? "N/A" : client.collateralToLoanRatio}
                                    </p>
                                    <p className="text-xs text-gray-500">
                                        {client.collateralToLoanRatio === null
                                            ? "No active loans"
                                            : client.collateralToLoanRatio >= 1.5
                                                ? "Well secured"
                                                : "Requires attention"}
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-center gap-3">
                                <Calendar className="w-4 h-4 text-gray-400" />
                                <div>
                                    <label className="text-sm font-medium text-gray-600">Months Since Last VAT Filing</label>
                                    <p className="text-gray-900 font-bold">{client.monthsSinceVATFiling} months</p>
                                    <p className="text-xs text-gray-500">
                                        {client.monthsSinceVATFiling <= 3
                                            ? "Compliant"
                                            : client.monthsSinceVATFiling <= 6
                                                ? "Attention needed"
                                                : "High risk"}
                                    </p>
                                </div>
                            </div>
                            {/*
                            <div>
                                <label className="text-sm font-medium text-gray-600">Description</label>
                                <p className="text-gray-700 text-sm mt-1">{client.description}</p>
                            </div>
                            */}
                        </CardContent>
                    </Card>

                    {/* Contact Information - Commented out */}
                    {/*
          <Card className="border border-gray-200">
            <CardHeader className="border-b border-gray-200">
              <CardTitle className="flex items-center gap-2">
                <Phone className="w-5 h-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="p-6 space-y-4">
              <div className="flex items-center gap-3">
                <Mail className="w-4 h-4 text-gray-400" />
                <div>
                  <label className="text-sm font-medium text-gray-600">Email</label>
                  <p className="text-gray-900">{client.email}</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Phone className="w-4 h-4 text-gray-400" />
                <div>
                  <label className="text-sm font-medium text-gray-600">Phone</label>
                  <p className="text-gray-900">{client.phone}</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <MapPin className="w-4 h-4 text-gray-400 mt-1" />
                <div>
                  <label className="text-sm font-medium text-gray-600">Address</label>
                  <p className="text-gray-900">{client.address}</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-600">Description</label>
                <p className="text-gray-700 text-sm mt-1">{client.description}</p>
              </div>
            </CardContent>
          </Card>
          */}
                </div>

                {/* Financial Overview & Transactions */}
                <div className="lg:col-span-2">
                    {/* Financial Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                        <Card className="border border-gray-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Current Balance</p>
                                        <p className="text-2xl font-bold text-gray-900">${client.account_balance.toLocaleString()}</p>
                                    </div>
                                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                        <DollarSign className="text-blue-600 w-6 h-6" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border border-gray-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Avg Balance (6 Months)</p>
                                        <p className="text-2xl font-bold text-green-600">
                                            ${client.averageBalance6Months.toLocaleString()}
                                        </p>
                                    </div>
                                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <TrendingUp className="text-green-600 w-6 h-6" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        <Card className="border border-gray-200">
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Min Balance (3 Months)</p>
                                        <p className="text-2xl font-bold text-amber-600">
                                            ${client.minimumBalance3Months.toLocaleString()}
                                        </p>
                                    </div>
                                    <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                                        <TrendingDown className="text-amber-600 w-6 h-6" />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Financial Ratio with Inflow/Outflow */}
                    <Card className="border border-gray-200 mb-6">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="w-5 h-5" />
                                Financial Analysis
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                {/* Inflow/Outflow Ratio */}
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Inflow/Outflow Ratio</p>
                                        <p className="text-3xl font-bold text-gray-900">{client.inflowOutflowRatio}</p>
                                        <p className="text-sm text-gray-500 mt-1">
                                            {Number.parseFloat(client.inflowOutflowRatio.toString()) >= 1.0
                                                ? "Positive cash flow"
                                                : "Negative cash flow"}
                                        </p>
                                    </div>
                                    <div
                                        className={`w-16 h-16 ${Number.parseFloat(client.inflowOutflowRatio.toString()) >= 1.0 ? "bg-green-100" : "bg-red-100"} rounded-lg flex items-center justify-center`}
                                    >
                                        {Number.parseFloat(client.inflowOutflowRatio.toString()) >= 1.0 ? (
                                            <TrendingUp className="text-green-600 w-8 h-8" />
                                        ) : (
                                            <TrendingDown className="text-red-600 w-8 h-8" />
                                        )}
                                    </div>
                                </div>

                                {/* Monthly Inflow */}
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Monthly Inflow</p>
                                        <p className="text-2xl font-bold text-green-600">${client.inflow.toLocaleString()}</p>
                                        <p className="text-sm text-gray-500 mt-1">Average monthly income</p>
                                    </div>
                                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                        <TrendingUp className="text-green-600 w-6 h-6" />
                                    </div>
                                </div>

                                {/* Monthly Outflow */}
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-sm font-medium text-gray-600">Monthly Outflow</p>
                                        <p className="text-2xl font-bold text-red-600">${client.outflow.toLocaleString()}</p>
                                        <p className="text-sm text-gray-500 mt-1">Average monthly expenses</p>
                                    </div>
                                    <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                        <TrendingDown className="text-red-600 w-6 h-6" />
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* POS Data */}
                    <Card className="border border-gray-200 mb-6">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="w-5 h-5" />
                                Point of Sales Data
                            </CardTitle>
                        </CardHeader>

                        {/* POS Metrics */}
                        <div className="p-6 border-b border-gray-200">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-blue-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-blue-600">Avg POS Volume (3 Months)</p>
                                            <p className="text-2xl font-bold text-blue-900">${client.avgPosVolume3Months.toLocaleString()}</p>
                                        </div>
                                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <TrendingUp className="text-blue-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-green-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-green-600">POS Growth Rate (3 Months)</p>
                                            <p
                                                className={`text-2xl font-bold ${
                                                    client.posGrowthRate3Months >= 0 ? "text-green-900" : "text-red-900"
                                                }`}
                                            >
                                                {`${client.posGrowthRate3Months >= 0 ? "+" : ""}${client.posGrowthRate3Months.toFixed(1)}%`}
                                            </p>
                                        </div>
                                        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                            <TrendingUp className="text-green-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-purple-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-purple-600">Chargeback Rate</p>
                                            <p className="text-2xl font-bold text-purple-900">{`${client.chargebackRate.toFixed(1)}%`}</p>
                                        </div>
                                        <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <CreditCard className="text-purple-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Terminal ID
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Transaction Count
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Volume
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Average Transaction
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Chargeback %
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                    {client.posData.map((pos) => (
                                        <tr key={pos.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pos.date}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pos.terminalId}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{pos.transactionCount}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                ${pos.volume.toLocaleString()}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ${pos.averageTransaction.toFixed(2)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {pos.chargeback.toFixed(2)}%
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Transactions */}
                    <Card className="border border-gray-200 mb-6">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <FileText className="w-5 h-5" />
                                Recent Transactions
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Type
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Description
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                    {client.recentTransactions.map((transaction) => (
                                        <tr key={transaction.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.date}</td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <Badge
                                                    variant="secondary"
                                                    className={
                                                        transaction.type === "Inflow" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                                                    }
                                                >
                                                    {transaction.type}
                                                </Badge>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                ${transaction.amount.toLocaleString()}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{transaction.description}</td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Loan History */}
                    <Card className="border border-gray-200">
                        <CardHeader className="border-b border-gray-200">
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="w-5 h-5" />
                                Loan History
                            </CardTitle>
                        </CardHeader>

                        {/* Loan Metrics Cards */}
                        <div className="p-6 border-b border-gray-200">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="bg-red-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-red-600">Repayment Missed (Last 12 Months)</p>
                                            <p className="text-2xl font-bold text-red-900">{client.repaymentMissed12Months}</p>
                                            <p className="text-sm text-gray-500 mt-1">
                                                {client.repaymentMissed12Months === 0
                                                    ? "Perfect record"
                                                    : client.repaymentMissed12Months <= 2
                                                        ? "Acceptable"
                                                        : "High risk"}
                                            </p>
                                        </div>
                                        <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                            <AlertTriangle className="text-red-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-blue-50 p-4 rounded-lg">
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <p className="text-sm font-medium text-blue-600">Finance Used (Last 12 Months)</p>
                                            <p className="text-2xl font-bold text-blue-900">
                                                KWD {client.financeUsed12Months.toLocaleString()}
                                            </p>
                                            <p className="text-sm text-gray-500 mt-1">Total financing utilized</p>
                                        </div>
                                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <DollarSign className="text-blue-600 w-6 h-6" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <CardContent className="p-0">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Amount
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Purpose
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Current Utilization
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Repaid Ratio
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                    {client.loanHistory.map((loan) => (
                                        <tr key={loan.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{loan.date}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{loan.amount}</td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <Badge variant="secondary" className={getLoanStatusColor(loan.status)}>
                                                    {loan.status}
                                                </Badge>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{loan.purpose}</td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div className="flex items-center">
                            <span
                                className={`font-medium ${loan.repaidRatio > 80 ? "text-red-600" : loan.repaidRatio > 50 ? "text-amber-600" : "text-green-600"}`}
                            >
                              {loan.currentUtilization}
                            </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div className="flex items-center">
                            <span
                                className={`font-medium ${loan.repaidRatio === 100 ? "text-green-600" : loan.repaidRatio > 50 ? "text-blue-600" : "text-amber-600"}`}
                            >
                              {loan.repaidRatio.toFixed(1)}%
                            </span>
                                                    {loan.repaidRatio === 100 && <CheckCircle className="w-4 h-4 text-green-600 ml-2" />}
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </table>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
}
